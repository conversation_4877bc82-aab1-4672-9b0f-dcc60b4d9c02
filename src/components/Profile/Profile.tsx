import React, { useState, useMemo, useEffect } from 'react';
import emailIcon from '@/assets/images/icon-email.svg';
import verifyIcon from '@/assets/images/icon-verify.svg';
import {
  Form,
  Button,
  Modal,
  Input,
  message,
  type InputRef,
  ConfigProvider,
} from 'antd';
import { api, loadingManager } from '@/services';
import EditButton from './coms/EditButton';
import {
  ArrowLeftOutlined,
  EditFilled,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import ImageUploadWithCrop from '@/components/ImageUploadWithCrop';
import { useLanguage } from '@/hooks/useLanguage';
import { getDisplayAddress } from '@/utils/utils';
import type { UserProfileResponse, UpdateProfileRequest } from '@/types/api';
import { useAuthStore } from '@/store/authStore';
import FormButton from '@/pages/Register/coms/FormButton';
import EditProfilePassword from './coms/EditProfilePassword';
import CountrySelector from '@/components/CountryStateSelector/CountrySelector';
import StateSelector from '@/components/CountryStateSelector/StateSelector';

interface ProfileProps {
  visible: boolean;
  onClose: () => void;
}
const { TextArea } = Input;

const Profile: React.FC<ProfileProps> = ({ visible, onClose }) => {
  const { t, isEnUS } = useLanguage();
  const { logout, isArtist } = useAuthStore();

  // 编辑状态管理
  const [editingFields, setEditingFields] = useState<Record<string, boolean>>(
    {}
  );
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  // 使用单一 Form 实例，符合 React 最佳实践
  const [form] = Form.useForm();

  // 开始编辑
  const startEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: true }));
  };
  // 取消编辑
  const cancelEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: false }));
  };
  // 保存编辑
  const saveEdit = async ({
    fieldName,
    requestData,
  }: {
    fieldName: string;
    requestData: UpdateProfileRequest;
  }) => {
    try {
      // 如果是alias字段，需要先验证
      if (fieldName === 'alias' && requestData.alias) {
        const aliasCheckResponse = await api.auth.checkAlias(requestData.alias);
        if (
          aliasCheckResponse.code === 200 &&
          aliasCheckResponse.body?.trueOrFalse === false
        ) {
          message.error(t('auth.register.step2.messages.aliasUnavailable'));
          return;
        }
      }

      const response = await api.user.updateProfile(requestData);
      if (response.code === 200) {
        setEditingFields(prev => ({ ...prev, [fieldName]: false }));
        message.success(t('common.saveSuccess'));
        // 更新本地状态
        if (userInfo) {
          setUserInfo(prev => (prev ? { ...prev, ...requestData } : prev));
        }
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error(t('common.saveFailed'));
    }
  };
  // 用户信息状态
  const [userInfo, setUserInfo] = useState<UserProfileResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取用户资料
  const fetchUserProfile = async () => {
    try {
      setError(null);
      const response = await api.user.getProfile();
      if (response.code === 200 && response.body) {
        setUserInfo(response.body);
      } else {
        const errorMsg = response.message || t('common.messages.loadFailed');
        setError(errorMsg);
        message.error(errorMsg);
      }
    } catch (error) {
      console.error('获取用户资料失败:', error);
      const errorMsg = t('common.messages.loadFailed');
      setError(errorMsg);
      message.error(errorMsg);
    }
  };

  // 组件挂载时获取用户资料
  useEffect(() => {
    if (visible) {
      fetchUserProfile();
    }
  }, [visible]);
  const displayAddress = useMemo(() => {
    return userInfo ? getDisplayAddress(userInfo) : '';
  }, [userInfo]);

  // 头像上传成功处理
  const handleAvatarUploadSuccess = async (url: string) => {
    try {
      const response = await api.user.updateProfile({ avatarUrl: url });
      if (response.code === 200) {
        message.success(t('common.saveSuccess'));
        // 更新本地状态
        if (userInfo) {
          setUserInfo(prev => (prev ? { ...prev, avatarUrl: url } : prev));
        }
      } else {
        message.error(response.message || t('common.saveFailed'));
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      message.error(t('common.saveFailed'));
    }
  };

  // 自定义上传请求
  const customAvatarUpload = async (options: any) => {
    const { file, onSuccess, onError } = options;

    try {
      const response = await api.user.uploadAvatar(file);
      if (response.code === 200 && response.body?.url) {
        onSuccess({ url: response.body.url });
      } else {
        onError(new Error(response.message || '上传失败'));
      }
    } catch (error) {
      console.error('头像上传失败:', error);
      onError(error);
    }
  };
  interface ProfileItem {
    label: string;
    value: string;
    fieldName: string;
  }

  // 生成显示模式的组件
  const generateProfileItem = (item: ProfileItem) => {
    return (
      <div className="flex items-start" key={item.fieldName}>
        <span className="text-label mr-6px w-100px text-right -ml-100px">
          {item.label}:
        </span>
        <div className="flex-1">
          <span className="text-white">{item.value}</span>
          <EditFilled
            className="ml-10px cursor-pointer hover:text-primary"
            onClick={() => startEdit(item.fieldName)}
          />
        </div>
      </div>
    );
  };

  // 渲染不同类型的表单项
  const renderFormItems = (
    type: 'input' | 'textarea' | 'name' | 'address' | 'email',
    item: ProfileItem
  ) => {
    if (type === 'input') {
      return (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">{item.label}</span>
          }
          name={item.fieldName}
        >
          <Input className="s-profile-input" size="large" />
        </Form.Item>
      );
    }

    if (type === 'textarea') {
      return (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">{item.label}</span>
          }
          name={item.fieldName}
        >
          <TextArea rows={4} className="s-profile-input" />
        </Form.Item>
      );
    }

    if (type === 'name') {
      const rules = [
        {
          min: 2,
          message: t('auth.register.step3.validation.nameLength', {
            min: 2,
            max: 12,
          }),
        },
        {
          max: 12,
          message: t('auth.register.step3.validation.nameLength', {
            min: 2,
            max: 12,
          }),
        },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5\s·'-]+$/,
          message: t('auth.register.step3.validation.namePattern'),
        },
      ];

      const firstNameItem = (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">
              {t('auth.register.step3.form.firstName')}
            </span>
          }
          name="firstName"
          key="firstName"
          rules={rules}
        >
          <Input
            placeholder={t('auth.register.step3.form.firstNamePlaceholder')}
            className="s-profile-input"
            size="large"
          />
        </Form.Item>
      );

      const lastNameItem = (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">
              {t('auth.register.step3.form.lastName')}
            </span>
          }
          name="lastName"
          key="lastName"
          rules={rules}
        >
          <Input
            placeholder={t('auth.register.step3.form.lastNamePlaceholder')}
            className="s-profile-input"
            size="large"
          />
        </Form.Item>
      );

      return isEnUS
        ? [firstNameItem, lastNameItem]
        : [lastNameItem, firstNameItem];
    }

    if (type === 'address') {
      return (
        <>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.address')}
              </span>
            }
            name="address"
          >
            <Input
              placeholder={t('auth.register.step3.form.addressPlaceholder')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.city')}
              </span>
            }
            name="city"
          >
            <Input
              placeholder={t('auth.register.step3.form.cityPlaceholder')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.country')}
              </span>
            }
            name="country"
          >
            <CountrySelector
              placeholder={t('common.form.selectCountry')}
              size="small"
              className="s-profile-selector !h-35px"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('common.state')}
              </span>
            }
            name="state"
          >
            <StateSelector
              form={form}
              placeholder={t('common.form.selectState')}
              size="small"
              className="s-profile-selector !h-35px"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('auth.register.step3.form.postalZipCode')}
              </span>
            }
            name="postalZipCode"
          >
            <Input
              placeholder={t(
                'auth.register.step3.form.postalZipCodePlaceholder'
              )}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
        </>
      );
    }

    if (type === 'email') {
      return (
        <>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('common.email')}
              </span>
            }
            name="email"
          >
            <Input
              placeholder={t('common.form.emailRequired')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <Form.Item
            label={
              <span className="text-label text-12px w-100px">
                {t('common.emailCode')}
              </span>
            }
            name="verificationCode"
          >
            <Input
              placeholder={t('auth.login.form.emailCodeLabel')}
              className="s-profile-input"
              size="large"
            />
          </Form.Item>
          <div className="pl-114px flex flex-col gap-12px">
            <div>
              <Button
                htmlType="button"
                size="small"
                type="primary"
                onClick={async () => {
                  try {
                    const emailValue = form.getFieldValue('email');
                    if (!emailValue) {
                      message.error(t('common.form.emailRequired'));
                      return;
                    }
                    const response = await api.user.sendChangeEmailOtp({
                      recipient: emailValue,
                    });
                    if (response.code === 200) {
                      message.success(
                        t('common.messages.verificationCodeSent')
                      );
                    } else {
                      message.error(
                        response.message || t('messages.sendFailed')
                      );
                    }
                  } catch (error) {
                    console.error('发送验证码失败:', error);
                    message.error(t('messages.sendFailed'));
                  }
                }}
                className="leading-24px rounded-2px text-12px text-white mb-15px hover:!text-white"
              >
                <img src={emailIcon} className="w-13px" alt="sendCode" />
                {t('common.verificationCode')}
              </Button>
            </div>
            <div className="flex gap-12px">
              <Button
                htmlType="button"
                size="small"
                type="primary"
                ghost
                onClick={() => {
                  cancelEdit(item.fieldName);
                }}
                className="leading-24px !text-label !border-label rounded-2px text-12px  mb-15px hover:!text-label"
              >
                {t('common.cancel')}
              </Button>
              <Button
                htmlType="button"
                size="small"
                type="primary"
                onClick={async () => {
                  try {
                    const values = await form.validateFields([
                      'email',
                      'verificationCode',
                    ]);
                    // 直接调用邮箱修改API
                    const response = await api.user.changeEmail({
                      newEmail: values.email,
                      verificationCode: values.verificationCode,
                    });
                    if (response.code === 200) {
                      setEditingFields(prev => ({ ...prev, email: false }));
                      message.success(t('common.saveSuccess'));
                      // 更新本地状态
                      if (userInfo) {
                        setUserInfo(prev =>
                          prev ? { ...prev, email: values.email } : prev
                        );
                      }
                    } else {
                      message.error(response.message || t('common.saveFailed'));
                    }
                  } catch (error) {
                    console.error('验证失败:', error);
                    message.error(t('common.saveFailed'));
                  }
                }}
                className="leading-24px rounded-2px text-12px text-white mb-15px hover:!text-white"
                icon={<CheckOutlined />}
              >
                {t('common.verify')}
              </Button>
            </div>
          </div>
        </>
      );
    }

    return null;
  };

  // 通用编辑组件 - 使用单一 form 实例和 initialValues
  const generateEditItem = (
    type: 'input' | 'textarea' | 'name' | 'address' | 'email',
    item: ProfileItem
  ) => {
    const handleSave = async (values: any) => {
      let requestData: any = {};

      if (type === 'name') {
        requestData = {
          firstName: values.firstName || '',
          lastName: values.lastName || '',
        };
      } else if (type === 'address') {
        requestData = {
          addressLine1: values.address || '',
          addressLine2: values.city || '',
          countryCode: values.country || '',
          stateProvince: values.state || '',
          postalZipCode: values.postalZipCode || '',
        };
      } else if (type === 'email') {
        // 邮箱编辑需要验证码
        try {
          const response = await api.user.changeEmail({
            newEmail: values.email,
            verificationCode: values.verificationCode,
          });
          if (response.code === 200) {
            setEditingFields(prev => ({ ...prev, [item.fieldName]: false }));
            message.success(t('common.saveSuccess'));
            // 更新本地状态
            if (userInfo) {
              setUserInfo(prev =>
                prev ? { ...prev, email: values.email } : prev
              );
            }
          } else {
            message.error(response.message || t('common.saveFailed'));
          }
        } catch (error) {
          console.error('邮箱修改失败:', error);
          message.error(t('common.saveFailed'));
        }
        return;
      } else {
        requestData = {
          [item.fieldName]: values[item.fieldName] || '',
        };
      }

      saveEdit({
        fieldName: item.fieldName,
        requestData,
      });
    };

    // 设置初始值
    const getInitialValues = () => {
      if (type === 'name') {
        return {
          firstName: userInfo?.firstName || '',
          lastName: userInfo?.lastName || '',
        };
      } else if (type === 'address') {
        return {
          address: userInfo?.addressLine1 || '',
          city: userInfo?.addressLine2 || '',
          country: userInfo?.countryCode || '',
          state: userInfo?.stateProvince || '',
          postalZipCode: userInfo?.postalZipCode || '',
        };
      } else if (type === 'email') {
        return {
          email: userInfo?.email || '',
          verificationCode: '',
        };
      } else {
        return {
          [item.fieldName]: item.value,
        };
      }
    };

    return (
      <div className="flex items-start -ml-106px" key={item.fieldName}>
        <div className="flex-1">
          <Form
            form={form}
            requiredMark={false}
            onFinish={handleSave}
            autoComplete="off"
            initialValues={getInitialValues()}
          >
            {renderFormItems(type, item)}
            {type !== 'email' && (
              <EditButton
                className="pl-114px"
                htmlType="submit"
                onCancel={() => cancelEdit(item.fieldName)}
              />
            )}
          </Form>
        </div>
      </div>
    );
  };

  /**
   * 使用 TypeScript 的 as const 和 typeof 语法，将数组直接转换为枚举类型
   */
  const profileNameList = [
    'alias',
    'name',
    'email',
    'address',
    'stageName',
    'bio',
  ] as const;
  type ProfileName = (typeof profileNameList)[number];

  interface ProfileListItem {
    name: ProfileName;
    editable: boolean;
    _id?: number;
    showElement: React.ReactNode;
    editElement?: React.ReactNode;
  }
  const renderProfileInfo = () => {
    let profileList: ProfileListItem[] = [
      {
        name: 'alias',
        editable: !!editingFields['alias'],
        showElement: generateProfileItem({
          label: t('common.alias'),
          value: userInfo?.alias || '',
          fieldName: 'alias',
        }),
        editElement: generateEditItem('input', {
          label: t('common.alias'),
          value: userInfo?.alias || '',
          fieldName: 'alias',
        }),
      },
      {
        name: 'name',
        editable: !!editingFields['name'],
        showElement: generateProfileItem({
          label: t('common.name'),
          value: userInfo?.displayName || '',
          fieldName: 'name',
        }),
        editElement: generateEditItem('name', {
          label: t('common.name'),
          value: userInfo?.displayName || '',
          fieldName: 'name',
        }),
      },
      {
        name: 'email',
        editable: !!editingFields['email'],
        showElement: generateProfileItem({
          label: t('common.email'),
          value: userInfo?.email || '',
          fieldName: 'email',
        }),
        editElement: generateEditItem('email', {
          label: t('common.email'),
          value: userInfo?.email || '',
          fieldName: 'email',
        }),
      },
      {
        name: 'address',
        editable: !!editingFields['address'],
        showElement: generateProfileItem({
          label: t('common.address'),
          value: displayAddress,
          fieldName: 'address',
        }),
        editElement: generateEditItem('address', {
          label: t('common.address'),
          value: displayAddress,
          fieldName: 'address',
        }),
      },
    ];
    const artistProfileList: ProfileListItem[] = [
      {
        name: 'stageName',
        editable: !!editingFields['stageName'],
        showElement: generateProfileItem({
          label: t('common.stageName'),
          value: userInfo?.stageName || '',
          fieldName: 'stageName',
        }),
        editElement: generateEditItem('input', {
          label: t('common.stageName'),
          value: userInfo?.stageName || '',
          fieldName: 'stageName',
        }),
      },
      {
        name: 'bio',
        editable: !!editingFields['bio'],
        showElement: generateProfileItem({
          label: t('common.artistBio'),
          value: userInfo?.bio || '',
          fieldName: 'bio',
        }),
        editElement: generateEditItem('textarea', {
          label: t('common.artistBio'),
          value: userInfo?.bio || '',
          fieldName: 'bio',
        }),
      },
    ];

    if (isArtist) {
      profileList = profileList.concat(artistProfileList);
    }
    profileList.forEach((item, index) => {
      item._id = index;
    });

    return (
      <div className="flex flex-col gap-15px">
        {profileList.map(item => {
          return item.editable ? item.editElement : item.showElement;
        })}
      </div>
    );
  };

  if (!visible) return null;

  // 渲染错误占位内容
  const renderErrorPlaceholder = () => {
    if (error) {
      return (
        <div className="flex flex-col justify-center items-center h-400px gap-4">
          <div className="text-red-400">{error}</div>
          <Button
            type="primary"
            onClick={() => {
              fetchUserProfile();
            }}
          >
            {t('common.messages.retry')}
          </Button>
          {/* <Button ghost onClick={onClose}>
            {t('common.close')}
          </Button> */}
        </div>
      );
    }
    return null;
  };

  // 渲染骨架屏内容
  const renderSkeletonContent = () => {
    return (
      <div className="flex flex-col items-center pt-55px w-500px mx-auto">
        {/* 头像区域 */}
        <div className="flex flex-col items-center mb-30px">
          <div className="w-230px h-230px rounded-full bg-gray-700 animate-pulse mb-4"></div>
        </div>

        {/* 用户信息区域 */}
        <div className="w-full text-12px">
          <div className="text-center text-white text-22px font-medium mb-30px">
            <div className="h-6 bg-gray-700 rounded animate-pulse w-32 mx-auto"></div>
          </div>

          {/* 基本信息骨架 */}
          <div className="flex flex-col gap-15px">
            {[
              { label: t('common.alias') },
              { label: t('common.name') },
              { label: t('common.email') },
              { label: t('common.address') },
              ...(isArtist
                ? [
                    { label: t('common.stageName') },
                    { label: t('common.artistBio') },
                  ]
                : []),
            ].map((item, index) => (
              <div className="flex items-start" key={index}>
                <span className="text-label mr-6px w-100px text-right -ml-100px">
                  {item.label}:
                </span>
                <div className="flex-1">
                  <div className="h-4 bg-gray-700 rounded animate-pulse w-full max-w-xs"></div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-80px flex justify-center gap-20px">
            <div className="h-10 bg-gray-700 rounded animate-pulse flex-1"></div>
            <div className="h-10 bg-gray-700 rounded animate-pulse flex-1"></div>
          </div>
        </div>
      </div>
    );
  };

  const errorContent = renderErrorPlaceholder();
  if (errorContent) {
    return (
      <Modal
        open={visible}
        onCancel={onClose}
        width={1000}
        className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-112px) "
        footer={null}
        keyboard={false}
        maskClosable={false}
        centered
        zIndex={800}
        closeIcon={
          <div className="flex items-center gap-2 hover:opacity-45">
            <ArrowLeftOutlined
              style={{ fontSize: '16px', color: 'var(--color-label)' }}
            />
            <span className="text-#B5B5B5 font-400 min-w-max">
              {t('common.back')}
            </span>
          </div>
        }
        title={
          <div className=" text-center text-white text-32px font-700">
            {t('common.profile')}
          </div>
        }
      >
        {errorContent}
      </Modal>
    );
  }
  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            optionLineHeight: '35px',
            optionHeight: 35,
          },
          Form: {
            fontSize: 12,
          },
        },
      }}
    >
      <Modal
        open={visible}
        onCancel={onClose}
        width={1000}
        className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-112px) "
        footer={null}
        keyboard={false}
        maskClosable={false}
        centered
        zIndex={800}
        closeIcon={
          <div className="flex items-center gap-2 hover:opacity-45">
            <ArrowLeftOutlined
              style={{ fontSize: '16px', color: 'var(--color-label)' }}
            />
            <span className="text-#B5B5B5 font-400 min-w-max">
              {t('common.back')}
            </span>
          </div>
        }
        title={
          <div className=" text-center text-white text-32px font-700">
            {t('common.profile')}
          </div>
        }
      >
        {!userInfo ? (
          renderSkeletonContent()
        ) : (
          <div className="flex flex-col items-center pt-55px w-500px mx-auto ">
            {/* 头像区域 */}
            <div className="flex flex-col items-center mb-30px">
              <ImageUploadWithCrop
                avatarUrl={userInfo?.avatarUrl || ''}
                avatarSize={230}
                userName={userInfo?.alias || ''}
                onUploadSuccess={handleAvatarUploadSuccess}
                customRequest={customAvatarUpload}
              />
            </div>

            {/* 用户信息区域 */}
            <div className="w-full text-12px">
              <div className="text-center text-white text-22px font-medium mb-30px">
                {userInfo?.displayName || ''}
              </div>
              <div>{renderProfileInfo()}</div>
              {/* 密码表单区域 */}
              <EditProfilePassword
                visible={showPasswordForm}
                onClose={() => setShowPasswordForm(false)}
              />
              <div className="mt-80px flex justify-center gap-20px">
                {!showPasswordForm && (
                  <FormButton
                    ghost
                    variant="outlined"
                    className="flex-1"
                    onClick={() => setShowPasswordForm(true)}
                  >
                    {t('common.changePassword')}
                  </FormButton>
                )}
                <FormButton type="primary" className="flex-1" onClick={logout}>
                  {t('common.navigation.logout')}
                </FormButton>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </ConfigProvider>
  );
};

export default Profile;
